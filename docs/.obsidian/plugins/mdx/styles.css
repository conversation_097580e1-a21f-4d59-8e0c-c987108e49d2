/* node_modules/.pnpm/@code-hike+mdx@0.8.3_react@18.2.0/node_modules/@code-hike/mdx/dist/index.css */
.ch-terminal {
  font-size: 14px;
  height: 100%;
  box-sizing: border-box;
  background: #1e1e1e;
  color: #fafafa;
  overflow: hidden;
  padding: 0 8px 8px;
  font-family:
    Ubuntu,
    Droid Sans,
    -apple-system,
    BlinkMacSystemFont,
    Segoe WPC,
    Segoe UI,
    sans-serif;
}
.ch-terminal-container .ch-frame-content {
  background-color: inherit;
}
.ch-terminal-prompt {
  color: #8fa2db;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.ch-terminal-content {
  margin: 0;
}
.ch-terminal-output {
  opacity: .66;
}
.ch-code-line-number {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  text-align: right;
  display: inline-block;
  box-sizing: border-box;
  padding-right: 1.5ch;
  font-variant-numeric: tabular-nums;
}
.ch-code-scroll-parent {
  display: block;
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
  letter-spacing: 0;
  border-radius: 0;
  background-color: unset;
  color: unset;
  padding: 0;
  box-sizing: content-box;
  border: none;
}
.ch-code-scroll-parent ::-moz-selection {
  background-color: var(--ch-selection-background);
  color: inherit;
}
.ch-code-scroll-parent ::selection {
  background-color: var(--ch-selection-background);
  color: inherit;
}
.ch-code-button {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
  cursor: pointer;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  font-size: inherit;
  position: absolute;
  top: 10px;
  right: 10px;
  width: 1.1em;
  height: 1.1em;
}
.ch-code-button:focus-visible {
  outline-color: currentColor;
}
.ch-no-scroll {
  overflow: hidden;
}
.ch-expand-dialog {
  height: 100vh;
  width: 100vw;
  max-width: 900px;
  border: 0;
  background-color: transparent;
}
.ch-expand-dialog::backdrop {
  background-color: rgba(0, 0, 0, .8);
}
.ch-expand-close {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
  cursor: pointer;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  font-size: inherit;
  position: absolute;
  top: 10px;
  right: 10px;
  color: #fff;
  width: 26px;
  height: 26px;
}
.ch-expand-close:focus-visible {
  outline-color: currentColor;
}
.ch-expand-dialog-content {
  color: #fff;
  position: absolute;
  inset: 40px;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid;
}
.ch-code-browser {
  display: flex;
  height: 100%;
  font-family:
    Ubuntu,
    Droid Sans,
    -apple-system,
    BlinkMacSystemFont,
    Segoe WPC,
    Segoe UI,
    sans-serif;
}
.ch-code-browser-sidebar {
  border-right: 1px solid;
  min-width: 100px;
  padding: 1em 0;
  font-size: .95rem;
}
.ch-code-browser-content {
  overflow: auto;
  flex: 1;
  padding: 1em;
  white-space: pre;
  font-family: monospace;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.2rem;
  letter-spacing: 0;
  position: relative;
}
.ch-code-browser-content ::-moz-selection {
  background-color: var(--ch-selection-background);
  color: inherit;
}
.ch-code-browser-content ::selection {
  background-color: var(--ch-selection-background);
  color: inherit;
}
.ch-code-browser-sidebar-file,
.ch-code-browser-sidebar-folder {
  padding: .1em 1em;
}
.ch-code-browser-sidebar-file {
  cursor: pointer;
}
.ch-code-browser-sidebar-file:hover {
  background-color: var(--ch-hover-background);
  color: var(--ch-hover-foreground);
}
.ch-code-browser-button {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
  cursor: pointer;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  font-size: inherit;
  width: 1.5em;
  height: 1.5em;
  min-width: 1.5em;
  min-height: 1.5em;
  position: absolute;
  right: .8em;
  top: .8em;
}
.ch-code-browser-button:focus-visible {
  outline-color: currentColor;
}
.ch-editor-tab {
  border-right: 1px solid #252526;
  min-width: -moz-fit-content;
  min-width: fit-content;
  flex-shrink: 1;
  position: relative;
  display: flex;
  white-space: nowrap;
  cursor: pointer;
  height: 100%;
  box-sizing: border-box;
  padding-left: 15px;
  padding-right: 15px;
  background-color: #2d2d2d;
  color: hsla(0, 0%, 100%, .5);
  min-width: 0;
  border-bottom: 1px solid;
}
.ch-editor-tab-active {
  background-color: #1e1e1e;
  color: #fff;
  min-width: unset;
}
.ch-editor-tab > div {
  margin-top: auto;
  margin-bottom: auto;
  font-size: 12px;
  line-height: 1.4em;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ch-editor-frame {
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  font-family:
    Ubuntu,
    Droid Sans,
    -apple-system,
    BlinkMacSystemFont,
    Segoe WPC,
    Segoe UI,
    sans-serif;
  -moz-column-break-inside: avoid;
  break-inside: avoid;
  --ch-title-bar-height:30px;
  height: 100%;
}
.ch-editor-frame .ch-frame-title-bar {
  background: none;
}
.ch-editor-terminal {
  position: absolute;
  overflow: hidden;
  bottom: 0;
  width: 100%;
  background-color: #1e1e1e;
  color: #e7e7e7;
  border-top: 1px solid hsla(0, 0%, 50.2%, .35);
  padding: 0 8px;
  box-sizing: border-box;
}
.ch-editor-terminal-tab {
  text-transform: uppercase;
  padding: 4px 10px 3px;
  font-size: 11px;
  line-height: 24px;
  display: flex;
}
.ch-editor-terminal-tab > span {
  border-bottom: 1px solid #e7e7e7;
}
.ch-editor-terminal-content {
  margin-top: 8px;
  height: calc(100% - 40px);
  box-sizing: border-box;
}
.ch-editor-terminal-content .ch-terminal {
  font-size: 12px;
  margin: 0;
}
.ch-editor-button {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
  cursor: pointer;
  color: inherit;
  padding: 0;
  border: none;
  font-size: inherit;
  width: 1.5em;
  height: 1.5em;
  min-width: 1.5em;
  min-height: 1.5em;
  margin: 0 .8em 0 0;
}
.ch-editor-button:focus-visible {
  outline-color: currentColor;
}
.ch-frame {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.ch-frame,
.ch-simple-frame {
  font-family:
    Ubuntu,
    Droid Sans,
    -apple-system,
    BlinkMacSystemFont,
    Segoe WPC,
    Segoe UI,
    sans-serif;
  -moz-column-break-inside: avoid;
  break-inside: avoid;
  --ch-title-bar-height:30px;
}
.ch-simple-frame {
  border-radius: 6px;
  overflow: hidden;
  box-shadow:
    0 13px 27px -5px rgba(50, 50, 93, .25),
    0 8px 16px -8px rgba(0, 0, 0, .3),
    0 -6px 16px -6px rgba(0, 0, 0, .025);
}
.ch-frame-content {
  background-color: var(--ch-content-background,#fafafa);
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
  min-height: 0;
}
.ch-frame-zoom {
  --ch-frame-zoom:1;
  overflow: auto;
  position: relative;
  width: calc(100%/var(--ch-frame-zoom));
  height: calc(100%/var(--ch-frame-zoom));
  transform: scale(var(--ch-frame-zoom));
  transform-origin: left top;
}
.ch-frame-title-bar {
  font-size: 12px;
  width: 100%;
  height: var(--ch-title-bar-height);
  min-height: var(--ch-title-bar-height);
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  background-color: var(--ch-content-background,#252526);
  color: #ebebed;
  position: relative;
}
.ch-frame-middle-bar {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 1.2em;
}
.ch-frame-left-bar,
.ch-frame-right-bar {
  flex-grow: 1;
  flex-basis: 1em;
  height: 100%;
  display: flex;
  align-items: center;
  width: -moz-max-content;
  width: max-content;
}
.ch-frame-buttons {
  margin: 0 .8em;
  flex-shrink: 0;
  height: 1em;
  width: 4.16em;
  display: flex;
}
.ch-frame-button {
  width: 1em;
  height: 1em;
  border: .08em solid;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
}
.ch-frame-button-space {
  width: .56em;
}
.ch-frame-button-left {
  border-color: #ce5347;
  background-color: #ed6b60;
}
.ch-frame-button-middle {
  border-color: #d6a243;
  background-color: #f5be4f;
}
.ch-frame-button-right {
  border-color: #58a942;
  background-color: #62c554;
}
.ch-mini-browser {
  height: 100%;
}
.ch-mini-browser .ch-frame-content iframe,
.ch-mini-browser .ch-frame-content video {
  border: none;
  position: absolute;
  height: 100%;
  width: 100%;
}
.ch-mini-browser .ch-frame-title-bar input {
  height: 1.4em;
  font-size: 1em;
  border-radius: .5em;
  border: none;
  box-shadow: none;
  flex: 1;
  padding: 0 10px;
  color: #544;
  min-width: 5px;
  width: 5px;
}
.ch-browser-button {
  margin: 0 1em;
  color: #999;
}
.ch-browser-back-button {
  margin-left: .2em;
}
.ch-browser-forward-button {
  margin-left: 0;
}
.ch-browser-open-button {
  color: inherit;
}
.ch-browser-open-icon {
  display: block;
}
.ch-spotlight {
  display: flex;
  gap: 1.1rem;
  margin: 1rem 0;
}
.ch-spotlight-tabs {
  display: flex;
  flex-flow: column;
  flex: 1;
  gap: .5rem;
  align-items: stretch;
}
.ch-spotlight-tab {
  border-radius: .25rem;
  margin: 0 -.5rem;
  padding: 0 .5rem;
  border: 1px solid #e3e3e3;
}
.ch-spotlight-tab:hover {
  border-color: #222;
}
.ch-spotlight-tab[data-selected] {
  border-color: #0070f3;
}
.ch-spotlight-sticker {
  position: sticky;
  top: 10vh;
  display: flex;
  align-self: stretch;
  flex-flow: column;
  justify-content: center;
  width: 420px;
  min-height: min(100%, 80vh);
  max-height: 80vh;
}
.ch-spotlight-sticker .ch-codeblock,
.ch-spotlight-sticker .ch-codegroup {
  width: 100%;
  min-width: 100%;
  min-height: min(100%, 80vh);
  max-height: 80vh;
  margin-top: 0;
  margin-bottom: 0;
  flex: 1;
}
.ch-spotlight-with-preview .ch-spotlight-sticker {
  height: 80vh;
  gap: .5rem;
}
.ch-spotlight-with-preview .ch-spotlight-sticker .ch-codeblock,
.ch-spotlight-with-preview .ch-spotlight-sticker .ch-codegroup {
  min-height: 0;
  flex: 1;
}
.ch-spotlight-with-preview .ch-spotlight-preview {
  height: 280px;
}
.ch-scrollycoding {
  display: flex;
  position: relative;
  margin: 1rem 0;
  gap: 1rem;
}
.ch-scrollycoding-content {
  box-sizing: border-box;
  flex: 1;
}
.ch-scrollycoding-step-content {
  border-radius: 8px;
  margin: 0 -.5rem;
  padding: 1rem .5rem;
  border: 1.5px solid transparent;
  min-height: 180px;
}
.ch-scrollycoding-step-content[data-selected] {
  border-color: #0070f3;
}
.ch-scrollycoding-step-content > :first-child {
  margin-top: 0;
}
.ch-scrollycoding-step-content > :last-child {
  margin-bottom: 0;
}
.ch-scrollycoding-sticker {
  position: sticky;
  top: 10vh;
  display: flex;
  align-self: start;
  flex-flow: column;
  justify-content: center;
  width: var(--ch-scrollycoding-sticker-width,420px);
  max-height: 80vh;
}
.ch-scrollycoding-with-preview .ch-scrollycoding-sticker {
  height: 80vh;
  gap: .5rem;
}
.ch-scrollycoding-with-preview .ch-scrollycoding-sticker .ch-codeblock,
.ch-scrollycoding-with-preview .ch-scrollycoding-sticker .ch-codegroup {
  flex: 1;
}
.ch-scrollycoding-with-preview .ch-scrollycoding-preview {
  height: 280px;
}
.ch-scrollycoding-sticker .ch-codeblock,
.ch-scrollycoding-sticker .ch-codegroup {
  width: 100%;
  min-width: 100%;
  min-height: var(--ch-scrollycoding-code-min-height,200px);
  max-height: 80vh;
  margin-top: 0;
  margin-bottom: 0;
}
.ch-scrollycoding-static .ch-preview {
  height: 150px;
}
.ch-slideshow {
  margin: 1rem 0;
}
.ch-slideshow-slide {
  display: flex;
  flex-flow: row;
  gap: .5rem;
  align-items: stretch;
  aspect-ratio: 16/9;
}
.ch-slideshow-slide .ch-codeblock,
.ch-slideshow-slide .ch-codegroup {
  flex: 2;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}
.ch-slideshow .ch-slideshow-preview {
  flex: 1;
  height: auto;
  min-width: 0;
}
.ch-slideshow-range {
  display: flex;
  flex-flow: row;
  gap: .5rem;
}
.ch-slideshow-range input {
  flex: 1;
}
.ch-slideshow-notes {
  border-radius: .25rem;
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid #e3e3e3;
}
.ch-slideshow-note {
  min-height: 140px;
  max-height: 140px;
  padding: .05px;
  overflow: auto;
}
.ch-codeblock,
.ch-codegroup,
.ch-preview {
  border-radius: 6px;
  overflow: hidden;
  height: -moz-max-content;
  height: max-content;
  box-shadow:
    0 13px 27px -5px rgba(50, 50, 93, .25),
    0 8px 16px -8px rgba(0, 0, 0, .3),
    0 -6px 16px -6px rgba(0, 0, 0, .025);
  print-color-adjust: exact;
}
.ch-codeblock > *,
.ch-codegroup > *,
.ch-preview > * {
  height: 100%;
  max-height: inherit;
  min-height: inherit;
}
.ch-codeblock,
.ch-codegroup {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.ch-inline-code > code {
  padding: .2em .15em;
  margin: .1em -.05em;
  border-radius: .25em;
  font-size: .9rem;
}
.ch-section-link,
.ch-section-link * {
  text-decoration: underline;
  -webkit-text-decoration-style: dotted;
  text-decoration-style: dotted;
  text-decoration-thickness: 1px;
  -webkit-text-decoration-color: var(--ch-code-foreground,currentColor);
  text-decoration-color: var(--ch-code-foreground,currentColor);
}
.ch-section-link[data-active=true] {
  background-color: rgba(186, 230, 253, .4);
}
.ch-section-link[data-active=true],
.ch-section-link[data-active=true] * {
  text-decoration-thickness: 1.5px;
}
.ch-code-inline-mark {
  border-radius: .25rem;
  padding: .2rem .15rem .1rem;
  margin: 0 -.15rem;
}
.ch-code-multiline-mark-border {
  width: 3px;
  height: 100%;
  position: absolute;
  left: 0;
}
.ch-code-multiline-mark .ch-code-button {
  font-size: 1.2em;
  position: absolute;
  right: 10px;
  top: 1px;
  display: none;
}
.ch-code-inline-link {
  text-decoration: underline;
  -webkit-text-decoration-style: dotted;
  text-decoration-style: dotted;
  color: inherit;
}
.ch-code-link :not(span) > span {
  text-decoration: underline;
  -webkit-text-decoration-style: dotted;
  text-decoration-style: dotted;
  color: inherit;
}

/* src/styles.css */
.yuleicul-obsidian-mdx {
  max-width: var(--file-line-width);
  margin-left: auto;
  margin-right: auto;
  overflow-wrap: break-word;
}
.yuleicul-obsidian-mdx code {
  font-family: var(--font-monospace);
}
