---
title: Architectural Decision Records (ADRs)
description: Log of all significant architectural decisions made during development.
created: 2025-05-25
updated: 2025-05-25
version: 0.1.0
status: Living
tags: [architecture, decisions]
authors: [nitishMehrotra]
---

> **📋 Purpose:** This document tracks all significant architectural decisions made during the WorkflowMapperAgent project development. Each decision includes context, options considered, and rationale.

---

## 🏗️ Decision Format

Each decision follows this structure:
- **ID**: ADR-XXX
- **Date**: YYYY-MM-DD
- **Status**: Proposed | Accepted | Deprecated | Superseded
- **Context**: What situation led to this decision?
- **Decision**: What did we decide?
- **Consequences**: What are the positive/negative outcomes?

---

## 📋 Decision Index

> **📁 Individual ADR Files**: Each architectural decision is documented in a separate file using the [`adr-template.mdx`](../templates/adr-template.mdx) template.

| ID | Title | Status | Date | File |
|----|-------|--------|------|------|
| ADR-001 | Monorepo Structure with pnpm Workspaces | ✅ Accepted | 2025-05-25 | [`adr-001-monorepo.mdx`](./adr-001-monorepo.mdx) |
| ADR-002 | TypeScript-First Development | ✅ Accepted | 2025-05-25 | [`adr-002-typescript.mdx`](./adr-002-typescript.mdx) |
| ADR-003 | JSON-LD for Graph Representation | ✅ Accepted | 2025-05-25 | [`adr-003-jsonld.mdx`](./adr-003-jsonld.mdx) |
| ADR-004 | Consolidated .gitignore Strategy | ✅ Accepted | 2025-05-29 | [`adr-004-consolidated-gitignore-strateg.mdx`](./adr-004-consolidated-gitignore-strateg.mdx) |
| ADR-005 | Express.js for Backend API | ✅ Accepted | 2025-05-25 | *To be created* |
| ADR-006 | React + Vite for Frontend | ✅ Accepted | 2025-05-25 | *To be created* |
| ADR-007 | Neo4j for Graph Database | ✅ Accepted | 2025-05-25 | *To be created* |
| ADR-008 | Docusaurus for Documentation Site | ✅ Accepted | 2025-01-25 | [`adr-007-docusaurus.mdx`](./adr-007-docusaurus.mdx) |

### Status Legend
- 🟡 **Proposed**: Under discussion
- ✅ **Accepted**: Approved and implemented
- ❌ **Rejected**: Decided against
- 🔄 **Superseded**: Replaced by newer decision
- ⚠️ **Deprecated**: No longer recommended

---

## 🔄 ADR Process

> **📋 Complete Process Documentation:** For comprehensive ADR creation, review, management processes, and all architectural decision workflows, see [Core Process Guidelines](../process/agent-rules/core.mdx#🏗️-architectural-decision-process).

### Quick Reference
- **Complete Process**: [Core Process Guidelines](../process/agent-rules/core.mdx#🏗️-architectural-decision-process)
- **Template**: [`templates/adr-template.mdx`](../templates/adr-template.mdx)
- **Naming Convention**: `adr-XXX-short-title.mdx`
- **Location**: This directory (`docs/tech-specs/adrs/`)
- **Status Flow**: Proposed → Accepted/Rejected/Superseded
- **Index Maintenance**: Add entry to table above when creating new ADRs

---

## 🔗 References

- [Architectural Decision Records](https://adr.github.io/)
- [ADR Tools](https://github.com/npryce/adr-tools)
- [When to Write an ADR](https://engineering.atspotify.com/2020/04/14/when-should-i-write-an-architecture-decision-record/)
