#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/husky@9.0.11/node_modules/husky/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/husky@9.0.11/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/husky@9.0.11/node_modules/husky/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/husky@9.0.11/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../husky/bin.mjs" "$@"
else
  exec node  "$basedir/../husky/bin.mjs" "$@"
fi
