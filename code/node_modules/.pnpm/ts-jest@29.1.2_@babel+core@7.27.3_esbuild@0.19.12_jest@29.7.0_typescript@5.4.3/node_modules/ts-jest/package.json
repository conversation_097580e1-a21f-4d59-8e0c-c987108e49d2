{"name": "ts-jest", "version": "29.1.2", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"ts-jest": "cli.js"}, "description": "A Jest transformer with source map support that lets you use Jest to test projects written in TypeScript", "scripts": {"prebuild": "rimraf dist coverage *.tgz", "build": "tsc -p tsconfig.build.json", "postbuild": "node scripts/post-build.js", "pretest": "tsc -p tsconfig.spec.json --noEmit && node scripts/prepare-test.js", "test": "jest", "test-examples": "node scripts/test-examples.js", "lint": "eslint --ext .js,.ts .", "lint-fix": "eslint --fix --ext .js,.ts .", "lint-prettier": "prettier '**/*.{yml,yaml,md}' --write", "lint-prettier-ci": "prettier '**/*.{yml,yaml,md}' --check", "doc": "cd website && npm run start", "doc:build": "cd website && npm run build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "prepare": "npm run build", "prepublishOnly": "npm run test", "preversion": "npm run test", "version": "npm run changelog && git add CHANGELOG.md", "raw:options": "node scripts/generate-raw-compiler-options.js", "update-e2e": "node scripts/update-e2e.js"}, "repository": {"type": "git", "url": "git+https://github.com/kulshekhar/ts-jest.git"}, "keywords": ["jest", "typescript", "sourcemap", "react", "testing"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/kulshekhar)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/huafu)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/ahnpnl)", "<PERSON> <<EMAIL>> (https://github.com/GeeWee)"], "license": "MIT", "bugs": {"url": "https://github.com/kulshekhar/ts-jest/issues"}, "homepage": "https://kulshekhar.github.io/ts-jest", "dependencies": {"bs-logger": "0.x", "fast-json-stable-stringify": "2.x", "jest-util": "^29.0.0", "json5": "^2.2.3", "lodash.memoize": "4.x", "make-error": "1.x", "semver": "^7.5.3", "yargs-parser": "^21.0.1"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.0 <8", "@jest/types": "^29.0.0", "babel-jest": "^29.0.0", "jest": "^29.0.0", "typescript": ">=4.3 <6"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@jest/types": {"optional": true}, "babel-jest": {"optional": true}, "esbuild": {"optional": true}}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "post-commit": "git reset"}}, "devDependencies": {"@commitlint/cli": "17.x", "@commitlint/config-angular": "^17.6.5", "@jest/transform": "^29.5.0", "@jest/types": "^29.5.0", "@types/babel__core": "7.x", "@types/cross-spawn": "latest", "@types/fs-extra": "latest", "@types/js-yaml": "latest", "@types/lodash.camelcase": "4.x", "@types/lodash.memoize": "4.x", "@types/lodash.set": "4.x", "@types/micromatch": "4.x", "@types/node": "18.16.18", "@types/react": "18.x", "@types/rimraf": "^3.0.2", "@types/semver": "latest", "@types/yargs": "latest", "@types/yargs-parser": "21.x", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "babel-jest": "^29.5.0", "conventional-changelog-cli": "2.x", "cross-spawn": "latest", "esbuild": "~0.17.19", "eslint": "^8.42.0", "eslint-config-prettier": "latest", "eslint-plugin-import": "latest", "eslint-plugin-jest": "latest", "eslint-plugin-jsdoc": "latest", "eslint-plugin-prefer-arrow": "latest", "eslint-plugin-prettier": "latest", "execa": "5.1.1", "fs-extra": "11.x", "glob": "^10.2.6", "glob-gitignore": "latest", "husky": "4.x", "jest": "^29.5.0", "jest-snapshot-serializer-raw": "^1.2.0", "js-yaml": "latest", "json-schema-to-typescript": "^13.0.2", "lint-staged": "latest", "lodash.camelcase": "^4.3.0", "lodash.set": "^4.3.2", "node-fetch": "^3.3.2", "prettier": "^2.8.8", "typescript": "~5.1.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": "^16.10.0 || ^18.0.0 || >=20.0.0"}}