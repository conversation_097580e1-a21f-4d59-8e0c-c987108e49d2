#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/ts-jest@29.1.2_@babel+core@7.27.3_esbuild@0.19.12_jest@29.7.0_typescript@5.4.3/node_modules/ts-jest/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/ts-jest@29.1.2_@babel+core@7.27.3_esbuild@0.19.12_jest@29.7.0_typescript@5.4.3/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/ts-jest@29.1.2_@babel+core@7.27.3_esbuild@0.19.12_jest@29.7.0_typescript@5.4.3/node_modules/ts-jest/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/ts-jest@29.1.2_@babel+core@7.27.3_esbuild@0.19.12_jest@29.7.0_typescript@5.4.3/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
