#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/esbuild@0.20.2/node_modules/esbuild/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/esbuild@0.20.2/node_modules/esbuild/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/esbuild@0.20.2/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/esbuild@0.20.2/node_modules/esbuild/bin/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/esbuild@0.20.2/node_modules/esbuild/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/esbuild@0.20.2/node_modules:/Users/<USER>/tmp/kloudi-swe-agent/code/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
"$basedir/../../../esbuild/bin/esbuild"   "$@"
exit $?
